import React, { useState } from 'react';
import { useDayCycle } from './Context/DayCycleContext';
import type { DayPhase } from './Hooks/useDayCycleTimer';

// 🌅 CONTRÔLEUR DE CYCLE DE JOURNÉE RÉVOLUTIONNAIRE V2
// Interface complète avec navigation, persistance et synchronisation parfaite

interface DayCycleControllerProps {
  className?: string;
  isVisible?: boolean;
  showAdvancedControls?: boolean; // 🔧 CISCO: Contrôles avancés optionnels
}

// 🎨 NOMS FRANÇAIS DES PHASES
const PHASE_NAMES_FR: Record<string, string> = {
  night: 'Nuit profonde',
  dawn: 'Aube',
  sunrise: 'Lever du soleil',
  morning: 'Matin',
  midday: 'Midi (zénith)',
  afternoon: 'Après-midi',
  sunset: 'Coucher du soleil',
  dusk: 'Crépuscule'
};

// 🎨 COULEURS PAR PHASE
const PHASE_COLORS: Record<string, string> = {
  night: 'from-indigo-900 to-purple-900',
  dawn: 'from-orange-200 to-pink-300',
  sunrise: 'from-orange-400 to-yellow-400',
  morning: 'from-yellow-300 to-blue-400',
  midday: 'from-blue-400 to-cyan-300',
  afternoon: 'from-orange-300 to-red-400',
  sunset: 'from-red-400 to-purple-500',
  dusk: 'from-purple-500 to-indigo-700'
};

// 🕐 FORMATAGE DU TEMPS
const formatTime = (ms: number): string => {
  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

const DayCycleController: React.FC<DayCycleControllerProps> = ({
  className = '',
  isVisible = true,
  showAdvancedControls = true
}) => {
  // 🎛️ État local pour l'interface
  const [showDetails, setShowDetails] = useState(false);
  const [showPhaseSelector, setShowPhaseSelector] = useState(false);

  // 🌅 CISCO: Utiliser le contexte global (plus de props drilling !)
  const {
    currentPhase,
    phaseIndex,
    progress,
    phaseProgress,
    elapsedTime,
    cycleDuration,
    status,
    currentPhaseEmoji,
    phaseNumber,
    totalPhases,
    phaseDuration,
    start,
    pause,
    resume,
    stop,
    reset,
    setCycleDuration,
    goToPhase,
    goToPreviousPhase,
    goToNextPhase,
    setPhaseProgress, // 🎛️ CISCO: Contrôle manuel de la progression
    isRunning,
    isPaused,
    isStopped,
    previousPhase,
    nextPhase,
    allPhases,
    phaseEmojis,
    isManualMode, // 🎛️ CISCO: Mode manuel
    manualPhaseProgress // 🎛️ CISCO: Progression manuelle
  } = useDayCycle();

  // 🎮 GESTION DES CONTRÔLES AMÉLIORÉE
  const handlePlayPause = () => {
    if (isRunning) {
      pause();
    } else if (isPaused) {
      resume();
    } else {
      start();
    }
  };

  const handleStop = () => {
    stop();
  };

  const handleReset = () => {
    reset();
  };

  const handleCycleDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDuration = parseInt(e.target.value);
    setCycleDuration(newDuration);
  };

  // 🎛️ CISCO: Gestion du curseur de progression de phase
  const handlePhaseProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newProgress = parseFloat(e.target.value) / 100; // Convertir de 0-100 à 0-1
    setPhaseProgress(newProgress);
  };

  // 🔧 CISCO: Nouvelles fonctions de navigation
  const handlePreviousPhase = () => {
    goToPreviousPhase();
  };

  const handleNextPhase = () => {
    goToNextPhase();
  };

  const handlePhaseSelect = (phase: DayPhase) => {
    goToPhase(phase);
    setShowPhaseSelector(false);
  };

  // 🔧 CISCO: Noms français des phases pour l'interface
  const PHASE_NAMES_FR: Record<DayPhase, string> = {
    dawn: 'Aube',
    sunrise: 'Lever du soleil',
    morning: 'Matin',
    midday: 'Midi',
    afternoon: 'Après-midi',
    sunset: 'Coucher du soleil',
    dusk: 'Crépuscule',
    night: 'Nuit'
  };

  // 🎨 CALCULS POUR L'AFFICHAGE
  const remainingPhaseTime = phaseDuration - (phaseProgress * phaseDuration);
  const remainingCycleTime = cycleDuration - elapsedTime;
  const cycleDurationMinutes = Math.round(cycleDuration / (60 * 1000));

  if (!isVisible) return null;

  return (
    <div className={`bg-black/90 backdrop-blur-sm rounded-xl p-6 text-white shadow-2xl border border-white/10 relative z-[70] ${className}`}>
      {/* 🎯 TITRE */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-teal-400 flex items-center gap-2">
          🌅 Temporisateur de Journée
        </h3>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-gray-400 hover:text-white transition-colors text-sm"
        >
          {showDetails ? '🔼 Masquer' : '🔽 Détails'}
        </button>
      </div>

      {/* 🌟 PHASE ACTUELLE */}
      <div className="text-center mb-6">
        <div className={`inline-flex items-center gap-3 px-4 py-2 rounded-full bg-gradient-to-r ${PHASE_COLORS[currentPhase]} text-white font-semibold shadow-lg`}>
          <span className="text-2xl">{currentPhaseEmoji}</span>
          <span className="text-lg">{PHASE_NAMES_FR[currentPhase]}</span>
          <span className="text-sm opacity-80">({phaseNumber}/{totalPhases})</span>
        </div>
      </div>

      {/* 📊 BARRE DE PROGRESSION GLOBALE */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-300 mb-1">
          <span>Progression du cycle</span>
          <span>{Math.round(progress * 100)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-teal-400 to-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress * 100}%` }}
          />
        </div>
      </div>

      {/* 📊 BARRE DE PROGRESSION DE PHASE */}
      <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-300 mb-1">
          <span>Progression de la phase</span>
          <span>{Math.round(phaseProgress * 100)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-1.5">
          <div 
            className={`bg-gradient-to-r ${PHASE_COLORS[currentPhase]} h-1.5 rounded-full transition-all duration-300`}
            style={{ width: `${phaseProgress * 100}%` }}
          />
        </div>
      </div>

      {/* 🎛️ CURSEUR DE DURÉE */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Durée du cycle complet : {cycleDurationMinutes} minutes
        </label>
        <input
          type="range"
          min="1"
          max="60"
          value={cycleDurationMinutes}
          onChange={handleCycleDurationChange}
          disabled={isRunning}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          style={{
            background: `linear-gradient(to right, #14b8a6 0%, #14b8a6 ${(cycleDurationMinutes - 1) / 59 * 100}%, #374151 ${(cycleDurationMinutes - 1) / 59 * 100}%, #374151 100%)`
          }}
        />
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>1 min</span>
          <span>Rapide ← → Lent</span>
          <span>60 min</span>
        </div>
      </div>

      {/* 🎛️ CISCO: CURSEUR DE PROGRESSION DE PHASE */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Progression de la phase : {(phaseProgress * 100).toFixed(1)}%
          <span className="text-xs text-gray-400 ml-2">
            ({currentPhaseEmoji} {PHASE_NAMES_FR[currentPhase]})
          </span>
        </label>
        <input
          type="range"
          min="0"
          max="100"
          step="0.1"
          value={phaseProgress * 100}
          onChange={handlePhaseProgressChange}
          disabled={isRunning}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          style={{
            background: `linear-gradient(to right, #f59e0b 0%, #f59e0b ${phaseProgress * 100}%, #374151 ${phaseProgress * 100}%, #374151 100%)`
          }}
        />
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>0%</span>
          <span>Début ← → Fin de phase</span>
          <span>100%</span>
        </div>
        {isManualMode && (
          <div className="text-xs text-amber-400 mt-1 flex items-center">
            🎛️ Mode manuel actif - Le timer reprendra depuis cette position
          </div>
        )}
      </div>

      {/* 🔧 CISCO: NAVIGATION ENTRE PHASES */}
      <div className="flex items-center justify-center gap-2 mb-4">
        {/* BOUTON PHASE PRÉCÉDENTE */}
        <button
          onClick={handlePreviousPhase}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-sm transition-all duration-300 shadow-md"
          title={`Phase précédente: ${phaseEmojis[previousPhase]} ${PHASE_NAMES_FR[previousPhase]}`}
        >
          ⬅️
        </button>

        {/* SÉLECTEUR DE PHASE RAPIDE */}
        <div className="relative">
          <button
            onClick={() => setShowPhaseSelector(!showPhaseSelector)}
            className="flex items-center justify-center px-3 py-2 rounded-lg bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white text-sm transition-all duration-300 shadow-md"
            title="Sélectionner une phase directement"
          >
            🎯 Saut
          </button>

          {/* MENU DÉROULANT DES PHASES - CISCO: Z-index élevé pour passer au-dessus de l'audio */}
          {showPhaseSelector && (
            <div className="absolute top-full left-0 mt-2 bg-black/95 backdrop-blur-sm rounded-lg border border-white/20 shadow-xl z-[70] min-w-48">
              {allPhases.map((phase) => (
                <button
                  key={phase}
                  onClick={() => handlePhaseSelect(phase)}
                  className={`
                    w-full px-3 py-2 text-left text-sm transition-colors duration-200 flex items-center gap-2
                    ${phase === currentPhase
                      ? 'bg-teal-600 text-white'
                      : 'text-gray-300 hover:bg-white/10 hover:text-white'
                    }
                    ${phase === allPhases[0] ? 'rounded-t-lg' : ''}
                    ${phase === allPhases[allPhases.length - 1] ? 'rounded-b-lg' : ''}
                  `}
                >
                  <span className="text-lg">{phaseEmojis[phase]}</span>
                  <span>{PHASE_NAMES_FR[phase]}</span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* BOUTON PHASE SUIVANTE */}
        <button
          onClick={handleNextPhase}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white text-sm transition-all duration-300 shadow-md"
          title={`Phase suivante: ${phaseEmojis[nextPhase]} ${PHASE_NAMES_FR[nextPhase]}`}
        >
          ➡️
        </button>
      </div>

      {/* 🎮 CONTRÔLES PRINCIPAUX */}
      <div className="flex items-center justify-center gap-4 mb-4">
        {/* BOUTON PLAY/PAUSE PRINCIPAL */}
        <button
          onClick={handlePlayPause}
          className={`
            flex items-center justify-center w-16 h-16 rounded-full text-2xl font-bold transition-all duration-300 shadow-lg
            ${isRunning
              ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white'
              : 'bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white'
            }
          `}
        >
          {isRunning ? '⏸️' : '▶️'}
        </button>

        {/* BOUTON STOP */}
        <button
          onClick={handleStop}
          disabled={isStopped}
          className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          ⏹️
        </button>

        {/* BOUTON RESET */}
        <button
          onClick={handleReset}
          className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white text-lg transition-all duration-300"
        >
          🔄
        </button>
      </div>

      {/* 📱 DÉTAILS AVANCÉS (PLIABLE) */}
      {showDetails && (
        <div className="border-t border-gray-700 pt-4 space-y-4">
          {/* Informations temporelles */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Temps écoulé :</span>
              <div className="text-teal-400 font-mono">{formatTime(elapsedTime)}</div>
            </div>
            <div>
              <span className="text-gray-400">Temps restant (phase) :</span>
              <div className="text-orange-400 font-mono">{formatTime(remainingPhaseTime)}</div>
            </div>
            <div>
              <span className="text-gray-400">Statut :</span>
              <div className={`font-semibold ${
                isRunning ? 'text-green-400' :
                isPaused ? 'text-orange-400' :
                'text-gray-400'
              }`}>
                {isRunning ? '🟢 En cours' : isPaused ? '🟡 En pause' : '🔴 Arrêté'}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Cycle restant :</span>
              <div className="text-blue-400 font-mono">{formatTime(Math.max(0, remainingCycleTime))}</div>
            </div>
          </div>

          {/* 🔧 CISCO: Informations de navigation */}
          <div className="border-t border-gray-600 pt-3">
            <div className="text-sm text-gray-400 mb-2">Navigation :</div>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-1">
                <span>{phaseEmojis[previousPhase]}</span>
                <span className="text-gray-500">{PHASE_NAMES_FR[previousPhase]}</span>
              </div>
              <div className="text-gray-600">←→</div>
              <div className="flex items-center gap-1">
                <span>{phaseEmojis[nextPhase]}</span>
                <span className="text-gray-500">{PHASE_NAMES_FR[nextPhase]}</span>
              </div>
            </div>
          </div>

          {/* 🔧 CISCO: Contrôles avancés */}
          {showAdvancedControls && (
            <div className="border-t border-gray-600 pt-3">
              <div className="text-sm text-gray-400 mb-2">Contrôles avancés :</div>
              <div className="flex gap-2">
                <button
                  onClick={() => setCycleDuration(1)}
                  className="px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
                  disabled={isRunning}
                >
                  🚀 Rapide (1min)
                </button>
                <button
                  onClick={() => setCycleDuration(30)}
                  className="px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
                  disabled={isRunning}
                >
                  🐌 Lent (30min)
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 💡 AIDE RAPIDE AMÉLIORÉE */}
      <div className="text-xs text-gray-500 text-center mt-4 space-y-1">
        <div>🌅 Temporisateur révolutionnaire avec navigation complète</div>
        <div className="text-gray-600">⬅️➡️ Navigation • 🎯 Saut direct • 💾 Sauvegarde automatique</div>
      </div>
    </div>
  );
};

export default DayCycleController;
